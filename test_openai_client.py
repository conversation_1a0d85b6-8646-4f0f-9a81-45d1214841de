import os
from openai import OpenAI

client = OpenAI(base_url="http://172.16.3.112:8080/v1", api_key="")

completion = client.chat.completions.create(
    model="MiniCPM-V-4_5-F16.gguf",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "<省略794152字符的图像数据>"
                    }
                },
                {
                    "type": "text",
                    "text": "检查所见"
                }
            ]
        },
        {
            "role": "assistant",
            "content": [
                {
                    "type": "text",
                    "text": "眼轴：右眼22.43mm,左眼22.52mm\n曲率：右眼K1（42.14D@174°），K2（45.55D@84°）\n左眼K1（42.12D@6°），K2（44.78D@96°）"
                }
            ]
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "<省略898304字符的图像数据>"
                    }
                },
                {
                    "type": "text",
                    "text": "[mm_media]1[/mm_media][mm_media]1[/mm_media][mm_media]1[/mm_media]检查所见"
                }
            ]
        }
    ]
)

print(completion.choices[0].message.content)
