#!/bin/bash

# PaddleOCR 表格服务停止脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/paddle_table.pid"

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "PID文件不存在，服务可能没有运行"
    exit 1
fi

# 读取PID
PID=$(cat "$PID_FILE")

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "进程 $PID 不存在，清理PID文件"
    rm -f "$PID_FILE"
    exit 1
fi

echo "正在停止 PaddleOCR 表格服务 (PID: $PID)..."

# 尝试优雅关闭
kill -TERM $PID

# 等待进程结束
for i in {1..10}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "✅ PaddleOCR 表格服务已成功停止"
        rm -f "$PID_FILE"
        exit 0
    fi
    echo "等待服务停止... ($i/10)"
    sleep 1
done

# 如果优雅关闭失败，强制终止
echo "优雅关闭超时，强制终止进程..."
kill -KILL $PID

# 再次检查
if ! ps -p $PID > /dev/null 2>&1; then
    echo "✅ PaddleOCR 表格服务已强制停止"
    rm -f "$PID_FILE"
    exit 0
else
    echo "❌ 无法停止服务，请手动处理"
    exit 1
fi
