import json

export_file = "/Users/<USER>/Downloads/project-3-at-2025-08-26-07-04-0b67892b.json"   # 你的导出文件
with open(export_file, "r", encoding="utf-8") as f:
    tasks = json.load(f)

total = 0
correct = 0
wrong = 0

for task in tasks:
    if not task.get("annotations"):
        continue
    for ann in task["annotations"]:
        for r in ann.get("result", []):
            if r.get("from_name") == "isCorrect":
                total += 1
                if "Correct" in r["value"].get("choices", []):
                    correct += 1
                if "Wrong" in r["value"].get("choices", []):
                    wrong += 1

print(f"总数: {total}")
print(f"Correct 数量: {correct}")
print(f"Correct 占比: {correct/total:.2%}" if total > 0 else "无标注")
print(f"Wrong 数量: {wrong}")
print(f"Wrong 占比: {wrong/total:.2%}" if total > 0 else "无标注")


