import time

from paddleocr import PPStructureV3

pipeline = PPStructureV3(use_doc_unwarping=False,
                         use_doc_orientation_classify=False,
                         use_textline_orientation=False,
                         use_seal_recognition=False,
                         use_formula_recognition=False,
                         use_chart_recognition=False,
                         layout_detection_model_name="PP-DocLayout_plus-L",
                         text_detection_model_name="PP-OCRv5_mobile_det",
                         text_recognition_model_name="PP-OCRv5_server_rec")
start_time = time.time()
output = pipeline.predict(input="/Users/<USER>/Downloads/d4e5bb0a-ac32-420a-a565-942d314795c0.pdf")
end_time = time.time()

print(f"{end_time - start_time:.3f} s")

for res in output:
    res.save_to_markdown(save_path="./")
