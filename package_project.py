#!/usr/bin/env python3
"""
OCR服务器代码混淆构建脚本
使用PyArmor混淆Python代码并创建便携式部署包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import glob
import platform

# 版本配置
VERSION = "1.0.0"
PROJECT_NAME = "ocr_server"

# 模型优化配置
MODEL_OPTIMIZATION = {
    'enabled': True,  # 设为True启用模型选择优化
    'keep_versions': ['ppocrv5'],  # 保留的模型版本 
    'keep_model_types': ['det', 'rec'],  # 保留的模型类型
    'prefer_server_models': False,  # True=保留server版本, False=保留普通版本
}


def check_pyarmor_available():
    """检查PyArmor是否可用"""
    try:
        result = subprocess.run(['pyarmor', '--version'],
                                capture_output=True, text=True, check=True)
        print(f"检测到PyArmor: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


def get_system_info():
    """获取系统信息"""
    system = platform.system().lower()
    machine = platform.machine().lower()

    if system == 'darwin':
        return 'macos', machine
    elif system == 'linux':
        return 'linux', machine
    elif system == 'windows':
        return 'windows', machine
    else:
        return system, machine


def copy_shell_scripts(build_dir):
    """复制shell脚本文件到构建目录"""
    print("复制shell脚本文件...")

    if os.path.exists("install.sh"):
        shutil.copy2("install.sh", build_dir / "install.sh")
        os.chmod(build_dir / "install.sh", 0o755)
        print("已复制 install.sh")
    else:
        print("install.sh 文件不存在")


def install_pyarmor_guide():
    """显示PyArmor安装指引"""
    print("未检测到PyArmor，代码将不会被混淆")
    print("如需使用代码混淆功能，请先安装PyArmor:")
    print("   pip install pyarmor")
    print("   或者")
    print("   pip3 install pyarmor")
    print("")

    print("选择操作:")
    print("  1. 自动安装PyArmor并继续")
    print("  2. 跳过混淆，直接打包")
    print("  3. 取消打包")

    while True:
        choice = input("请选择 [1/2/3]: ").strip()

        if choice == '1':
            print("正在尝试安装PyArmor...")
            # 尝试使用pip3，如果失败则使用pip
            pip_commands = ['pip3 install pyarmor', 'pip install pyarmor']

            for cmd in pip_commands:
                result = run_command(cmd, f"安装PyArmor ({cmd})")
                if result:
                    print("PyArmor安装成功！")
                    return True

            print("PyArmor安装失败，将跳过混淆继续打包")
            return True

        elif choice == '2':
            print("跳过混淆，将直接复制Python文件")
            return True

        elif choice == '3':
            print("用户取消打包")
            return False

        else:
            print("无效选择，请输入 1、2 或 3")


def run_command(cmd, description):
    """执行命令并处理错误"""
    print(f"{description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"{description}完成")
        return result
    except subprocess.CalledProcessError as e:
        print(f"{description}失败: {e}")
        print(f"错误输出: {e.stderr}")
        return None


def run_command_with_output(cmd, description):
    """执行命令并显示实时输出"""
    print(f"{description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True)
        print(f"{description}完成")
        return result
    except subprocess.CalledProcessError as e:
        print(f"{description}失败: {e}")
        return None


def get_dir_size(directory):
    """获取目录大小"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except (OSError, PermissionError):
        pass
    return total_size


def format_size(size_bytes):
    """格式化文件大小显示"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f}{unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f}TB"


def install_dependencies_optimized(lib_dir):
    """下载依赖包到lib目录，用于离线安装"""
    print("开始下载依赖包...")

    if not os.path.exists("requirements.txt"):
        print("requirements.txt 文件不存在")
        return False

    # 使用pip download下载所有依赖到lib目录
    print("使用pip download下载依赖包...")
    result = run_command_with_output(
        f"pip download -r requirements.txt -d {lib_dir}",
        "下载依赖包到lib目录"
    )

    if result:
        print("依赖包下载完成")

        # 统计下载的包数量和大小
        lib_files = list(lib_dir.glob("*"))
        package_count = len(lib_files)
        total_size = sum(f.stat().st_size for f in lib_files if f.is_file())

        print(f"下载统计:")
        print(f"  包数量: {package_count}")
        print(f"  总大小: {format_size(total_size)}")
        return True
    else:
        print("依赖包下载失败")
        return False


def optimize_models(models_dir):
    """根据配置优化模型文件，移除不需要的模型版本"""
    if not MODEL_OPTIMIZATION['enabled']:
        print("模型优化已禁用，保留所有模型文件")
        return

    print("开始模型文件优化...")

    original_size = get_dir_size(models_dir)
    removed_size = 0
    removed_count = 0

    keep_versions = MODEL_OPTIMIZATION['keep_versions']
    keep_types = MODEL_OPTIMIZATION['keep_model_types']
    prefer_server = MODEL_OPTIMIZATION['prefer_server_models']

    for version_dir in models_dir.iterdir():
        if version_dir.is_dir():
            version_name = version_dir.name

            # 检查版本是否需要保留
            if version_name not in keep_versions:
                size = get_dir_size(version_dir)
                shutil.rmtree(version_dir, ignore_errors=True)
                removed_size += size
                removed_count += 1
                print(f"  移除模型版本: {version_name} ({format_size(size)})")
            else:
                # 在保留的版本中优化模型类型
                for model_type_dir in version_dir.iterdir():
                    if model_type_dir.is_dir():
                        type_name = model_type_dir.name

                        # 检查模型类型是否需要保留
                        if type_name not in keep_types:
                            size = get_dir_size(model_type_dir)
                            shutil.rmtree(model_type_dir, ignore_errors=True)
                            removed_size += size
                            print(f"  移除模型类型: {version_name}/{type_name} ({format_size(size)})")
                        else:
                            # 在保留的类型中选择server vs 普通版本
                            model_files = list(model_type_dir.glob("*.onnx"))
                            if len(model_files) > 1:
                                # 有多个模型文件，选择保留哪个
                                server_models = [f for f in model_files if 'server' in f.name]
                                normal_models = [f for f in model_files if 'server' not in f.name]

                                files_to_remove = []
                                if prefer_server and server_models:
                                    files_to_remove = normal_models
                                elif not prefer_server and normal_models:
                                    files_to_remove = server_models

                                for file_to_remove in files_to_remove:
                                    size = file_to_remove.stat().st_size
                                    file_to_remove.unlink()
                                    removed_size += size
                                    print(f"  移除模型文件: {file_to_remove.name} ({format_size(size)})")

    final_size = get_dir_size(models_dir)

    if removed_size > 0:
        print(f"模型优化完成!")
        print(f"  优化前大小: {format_size(original_size)}")
        print(f"  优化后大小: {format_size(final_size)}")
        print(f"  节省空间: {format_size(removed_size)} ({(removed_size / original_size * 100):.1f}%)")
    else:
        print("没有需要移除的模型文件")


def main():
    print(f"开始构建混淆版{PROJECT_NAME} v{VERSION}...")

    # 检查PyArmor是否可用
    use_pyarmor = check_pyarmor_available()
    if not use_pyarmor:
        print("打包已取消")
        return False

    # 创建构建目录，使用版本号命名加_build后缀，版本号中的点号改为下划线
    version_formatted = VERSION.replace('.', '_')
    build_dir = Path(f"{PROJECT_NAME}_{version_formatted}_build")
    if build_dir.exists():
        print("🧹 清理旧的构建目录...")
        shutil.rmtree(build_dir)

    build_dir.mkdir()

    # 1. 处理Python文件（混淆或直接复制）
    def find_python_files():
        """查找当前目录及子目录下的所有Python文件，排除指定文件"""
        exclude_files = {'test_ocr.py', 'package_project.py'}  # 排除的文件
        exclude_dirs = {'__pycache__', '.git', '.vscode', 'logs', 'cut-images', 'images'}  # 排除的目录

        python_files = []
        for root, dirs, files in os.walk('.'):
            # 排除指定目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]

            for file in files:
                if file.endswith('.py') and file not in exclude_files:
                    rel_path = os.path.relpath(os.path.join(root, file), '.')
                    python_files.append(rel_path)

        return sorted(python_files)

    python_files = find_python_files()
    print(f"发现 {len(python_files)} 个Python文件需要处理")

    if use_pyarmor:
        print("开始混淆Python代码...")

        failed_files = []
        success_count = 0

        for py_file in python_files:
            py_path = Path(py_file)
            target_dir = build_dir / py_path.parent
            target_dir.mkdir(parents=True, exist_ok=True)

            # 使用pyarmor混淆单个文件
            result = run_command(
                f"pyarmor gen --output {target_dir} {py_file}",
                f"混淆 {py_file}"
            )

            if result:
                success_count += 1
            else:
                print(f"混淆失败，直接复制: {py_file}")
                shutil.copy2(py_file, build_dir / py_file)
                failed_files.append(py_file)

        print(f"混淆完成: 成功 {success_count}, 失败 {len(failed_files)}")
        if failed_files:
            print(f"混淆失败的文件: {', '.join(failed_files)}")

    else:
        print("直接复制Python代码（无混淆）...")

        for py_file in python_files:
            py_path = Path(py_file)
            target_dir = build_dir / py_path.parent
            target_dir.mkdir(parents=True, exist_ok=True)

            shutil.copy2(py_file, build_dir / py_file)
            print(f"已复制 {py_file}")

    # 2. 复制必要的非Python文件
    print("复制必要文件...")

    # 复制配置文件
    config_files = [
        "default_rapidocr.yaml",
        "requirements.txt"
    ]

    for file in config_files:
        if os.path.exists(file):
            shutil.copy2(file, build_dir / file)
            print(f"已复制 {file}")

    # 复制启动停止脚本（原始的install.sh将被增强版替换）
    script_files = [
        "start.sh",
        "stop.sh"
    ]

    for script in script_files:
        if os.path.exists(script):
            target_name = script
            shutil.copy2(script, build_dir / target_name)
            os.chmod(build_dir / target_name, 0o755)
            print(f"已复制 {script} -> {target_name}")

    # 3. 复制shell脚本文件
    copy_shell_scripts(build_dir)

    # 复制模型文件和字体文件
    if os.path.exists("inference/models"):
        shutil.copytree("inference/models", build_dir / "inference" / "models", dirs_exist_ok=True)
        print("已复制模型文件")

    if os.path.exists("inference/fonts"):
        shutil.copytree("inference/fonts", build_dir / "inference" / "fonts", dirs_exist_ok=True)
        print("已复制字体文件")

    # 创建日志目录
    (build_dir / "logs").mkdir(exist_ok=True)

    # 4. 下载依赖包到lib目录
    lib_dir = build_dir / "lib"
    lib_dir.mkdir(exist_ok=True)

    success = install_dependencies_optimized(lib_dir)
    if not success:
        print("依赖下载失败")
        return False

    # 5. 优化模型文件
    optimize_models(build_dir / "inference" / "models")

    # 9. 压缩打包
    final_size = get_dir_size(build_dir)
    print(f"\n开始压缩打包...")

    # 创建压缩包名称
    package_name = f"{PROJECT_NAME}_{version_formatted}"

    # 尝试创建tar.gz包（通常压缩率更好）
    tar_file = f"{package_name}.tar.gz"
    print(f"创建tar.gz压缩包: {tar_file}")
    result = run_command(
        f"cd {build_dir.parent} && tar -czf {tar_file} {build_dir.name}",
        "创建tar.gz压缩包"
    )

    if result:
        tar_size = os.path.getsize(tar_file)
        compression_ratio = (1 - tar_size / final_size) * 100
        print(f"tar.gz压缩包创建成功")
        print(f"   原始大小: {format_size(final_size)}")
        print(f"   压缩后大小: {format_size(tar_size)}")
        print(f"   压缩率: {compression_ratio:.1f}%")
    else:
        print("tar.gz压缩失败，尝试创建zip包...")

        # 备用方案：创建zip包
        zip_file = f"{package_name}.zip"
        print(f"创建zip压缩包: {zip_file}")
        result = run_command(
            f"cd {build_dir.parent} && zip -r -9 {zip_file} {build_dir.name}",
            "创建zip压缩包（最高压缩率）"
        )

        if result:
            zip_size = os.path.getsize(zip_file)
            compression_ratio = (1 - zip_size / final_size) * 100
            print(f"zip压缩包创建成功")
            print(f"   原始大小: {format_size(final_size)}")
            print(f"   压缩后大小: {format_size(zip_size)}")
            print(f"   压缩率: {compression_ratio:.1f}%")
        else:
            print("压缩打包失败")

    # 10. 显示最终构建信息
    print(f"\n混淆构建完成！")
    print(f"构建目录: {build_dir}")
    print(f"构建大小: {format_size(final_size)}")

    # 显示可用的压缩包
    available_packages = []
    if os.path.exists(tar_file):
        available_packages.append(tar_file)
    if os.path.exists(f"{package_name}.zip"):
        available_packages.append(f"{package_name}.zip")

    if available_packages:
        print(f"可用压缩包:")
        for pkg in available_packages:
            pkg_size = os.path.getsize(pkg)
            print(f"   {pkg} ({format_size(pkg_size)})")

    print("测试启动:")
    print(f"   cd {build_dir}")
    print("   ./install.sh")
    print("   ./start.sh")
    print("\n部署说明:")
    print("   1. 解压压缩包到目标机器")
    print("   2. 确保目标机器有Python3和pip3环境")
    print("   3. 运行 ./install.sh 自动安装所有依赖")
    print("   4. 运行 ./start.sh 启动服务")

    return True


if __name__ == "__main__":
    main()
