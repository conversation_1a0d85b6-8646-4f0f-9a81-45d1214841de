"""
异步图片保存模块

该模块提供异步保存图片的功能，包括：
- 异步保存图片到指定目录
- 自动创建和管理存储目录
- 基于文件夹大小的自动清理机制
- 删除过期文件的功能
"""

import os
import cv2
import asyncio
import threading
from datetime import datetime, timedelta
from pathlib import Path
import logging

# 获取logger
logger = logging.getLogger(__name__)


def get_folder_size(folder_path):
    """
    计算文件夹大小（字节）
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        int: 文件夹总大小（字节）
    """
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for f in filenames:
                fp = os.path.join(dirpath, f)
                if os.path.exists(fp):
                    total_size += os.path.getsize(fp)
    except Exception as e:
        logger.error(f"计算文件夹大小时出错: {e}")
    return total_size


def cleanup_old_images(folder_path, days=7, file_pattern="*.png"):
    """
    删除指定天数之前的图片文件（适用于平铺目录结构）
    
    Args:
        folder_path: 文件夹路径
        days: 删除多少天之前的文件，默认7天
        file_pattern: 文件匹配模式，默认"*.png"
        
    Returns:
        tuple: (删除文件数量, 释放的空间大小(字节))
    """
    try:
        cutoff_time = datetime.now() - timedelta(days=days)
        deleted_count = 0
        deleted_size = 0
        
        for file_path in Path(folder_path).glob(file_pattern):
            try:
                file_stats = file_path.stat()
                file_created_time = datetime.fromtimestamp(file_stats.st_ctime)
                
                if file_created_time < cutoff_time:
                    file_size = file_stats.st_size
                    file_path.unlink()
                    deleted_count += 1
                    deleted_size += file_size
                    logger.info(f"删除过期图片: {file_path.name}")
            except Exception as e:
                logger.error(f"删除文件 {file_path} 时出错: {e}")
        
        if deleted_count > 0:
            logger.info(f"清理完成，删除了 {deleted_count} 个文件，释放空间 {deleted_size / (1024*1024):.2f} MB")
        
        return deleted_count, deleted_size
    except Exception as e:
        logger.error(f"清理旧图片时出错: {e}")
        return 0, 0


def get_date_directories_sorted(base_folder):
    """
    获取按日期排序的目录列表（从最早到最新）
    目录结构：base_folder/yyyy/MM/dd/files
    
    Args:
        base_folder: 基础文件夹路径
        
    Returns:
        list: 按日期排序的目录信息列表，每个元素包含 {path, date_str, size}
    """
    try:
        base_path = Path(base_folder)
        if not base_path.exists():
            return []
        
        date_dirs = []
        
        # 遍历所有年份目录
        for year_dir in base_path.iterdir():
            if not year_dir.is_dir() or not year_dir.name.isdigit():
                continue
                
            # 遍历所有月份目录
            for month_dir in year_dir.iterdir():
                if not month_dir.is_dir() or not month_dir.name.isdigit():
                    continue
                    
                # 遍历所有日期目录
                for day_dir in month_dir.iterdir():
                    if not day_dir.is_dir() or not day_dir.name.isdigit():
                        continue
                    
                    # 检查目录是否有文件
                    has_files = any(day_dir.glob("*.png"))
                    if has_files:
                        date_str = f"{year_dir.name}-{month_dir.name.zfill(2)}-{day_dir.name.zfill(2)}"
                        dir_size = get_folder_size(day_dir)
                        
                        date_dirs.append({
                            'path': day_dir,
                            'date_str': date_str,
                            'size': dir_size
                        })
        
        # 按日期字符串排序（最早的在前面）
        date_dirs.sort(key=lambda x: x['date_str'])
        return date_dirs
        
    except Exception as e:
        logger.error(f"获取日期目录时出错: {e}")
        return []


def get_files_in_directory_sorted(directory_path, file_pattern="*.png"):
    """
    获取目录中按创建时间排序的文件列表（从最早到最新）
    
    Args:
        directory_path: 目录路径
        file_pattern: 文件匹配模式，默认"*.png"
        
    Returns:
        list: 按创建时间排序的文件路径列表，每个元素包含 {path, size, created_time}
    """
    try:
        directory = Path(directory_path)
        if not directory.exists() or not directory.is_dir():
            return []
        
        files_info = []
        
        for file_path in directory.glob(file_pattern):
            if file_path.is_file():
                try:
                    file_stats = file_path.stat()
                    files_info.append({
                        'path': file_path,
                        'size': file_stats.st_size,
                        'created_time': file_stats.st_ctime
                    })
                except Exception as e:
                    logger.error(f"获取文件 {file_path} 信息时出错: {e}")
        
        # 按创建时间排序（最早的在前面）
        files_info.sort(key=lambda x: x['created_time'])
        return files_info
        
    except Exception as e:
        logger.error(f"获取目录文件时出错: {e}")
        return []


def cleanup_images_by_size(base_folder, max_size_mb=100, file_pattern="*.png"):
    """
    基于文件夹总大小的智能清理函数
    清理策略：
    1. 从最早的日期目录开始判断
    2. 如果 总大小-该目录大小 > max_size_mb，则直接删除整个目录
    3. 否则进入该目录，逐个删除最早的文件，直到总大小 <= max_size_mb
    
    Args:
        base_folder: 基础文件夹路径
        max_size_mb: 最大大小限制(MB)，默认100MB
        file_pattern: 文件匹配模式，默认"*.png"
        
    Returns:
        tuple: (删除文件数量, 释放的空间大小(字节))
    """
    try:
        base_path = Path(base_folder)
        if not base_path.exists():
            return 0, 0
        
        max_size_bytes = max_size_mb * 1024 * 1024
        deleted_count = 0
        deleted_size = 0
        empty_dirs = []
        
        # 获取当前总大小
        current_total_size = get_folder_size(base_path)
        
        # 如果当前大小不超过限制，直接返回
        if current_total_size <= max_size_bytes:
            return 0, 0
        
        logger.info(f"开始智能清理，当前大小: {current_total_size / (1024*1024):.2f}MB，目标大小: {max_size_mb}MB")
        
        # 获取按日期排序的目录列表（从最早到最新）
        date_dirs = get_date_directories_sorted(base_folder)
        
        for dir_info in date_dirs:
            # 如果已经达到目标大小，停止清理
            if current_total_size <= max_size_bytes:
                break
            
            dir_path = dir_info['path']
            dir_size = dir_info['size']
            date_str = dir_info['date_str']
            
            # 策略1: 如果删除整个目录后仍超过限制，直接删除整个目录
            if (current_total_size - dir_size) > max_size_bytes:
                try:
                    # 统计目录中的文件数量
                    files_in_dir = list(dir_path.glob(file_pattern))
                    dir_file_count = len(files_in_dir)
                    
                    # 删除整个目录
                    import shutil
                    shutil.rmtree(dir_path)
                    
                    deleted_count += dir_file_count
                    deleted_size += dir_size
                    current_total_size -= dir_size
                    
                    logger.info(f"删除整个日期目录: {date_str} ({dir_size / (1024*1024):.2f}MB, {dir_file_count} 个文件)")
                    
                    # 标记父目录为可能的空目录
                    parent_dirs = [dir_path.parent, dir_path.parent.parent]
                    for parent_dir in parent_dirs:
                        if parent_dir != base_path and parent_dir not in empty_dirs:
                            empty_dirs.append(parent_dir)
                    
                except Exception as e:
                    logger.error(f"删除目录 {dir_path} 时出错: {e}")
            
            # 策略2: 删除整个目录会超过目标，进入目录逐个删除最早的文件
            else:
                try:
                    files_info = get_files_in_directory_sorted(dir_path, file_pattern)
                    
                    for file_info in files_info:
                        # 如果已经达到目标大小，停止删除
                        if current_total_size <= max_size_bytes:
                            break
                        
                        file_path = file_info['path']
                        file_size = file_info['size']
                        
                        try:
                            file_path.unlink()
                            deleted_count += 1
                            deleted_size += file_size
                            current_total_size -= file_size
                            
                            logger.info(f"删除文件: {file_path.name} ({file_size / 1024:.1f}KB)")
                            
                        except Exception as e:
                            logger.error(f"删除文件 {file_path} 时出错: {e}")
                    
                    # 检查目录是否为空
                    if not any(dir_path.glob(file_pattern)):
                        empty_dirs.append(dir_path)
                        # 也检查父目录
                        parent_dirs = [dir_path.parent, dir_path.parent.parent]
                        for parent_dir in parent_dirs:
                            if parent_dir != base_path and parent_dir not in empty_dirs:
                                empty_dirs.append(parent_dir)
                
                except Exception as e:
                    logger.error(f"清理目录 {dir_path} 中的文件时出错: {e}")
        
        # 删除空目录（从最深层开始）
        for empty_dir in sorted(set(empty_dirs), key=lambda x: len(x.parts), reverse=True):
            try:
                if empty_dir.exists() and not any(empty_dir.iterdir()):
                    empty_dir.rmdir()
                    logger.info(f"删除空目录: {empty_dir}")
            except Exception as e:
                logger.error(f"删除空目录 {empty_dir} 时出错: {e}")
        
        final_size = get_folder_size(base_path)
        logger.info(f"智能清理完成，删除了 {deleted_count} 个文件，释放空间 {deleted_size / (1024*1024):.2f}MB")
        logger.info(f"清理后大小: {final_size / (1024*1024):.2f}MB")
        
        return deleted_count, deleted_size
        
    except Exception as e:
        logger.error(f"智能清理时出错: {e}")
        return 0, 0


def cleanup_old_images_by_date(base_folder, days=7, file_pattern="*.png"):
    """
    删除指定天数之前的图片文件（适用于按日期组织的目录结构）
    目录结构：base_folder/yyyy/MM/dd/files
    注意：这是旧的按时间清理函数，建议使用 cleanup_images_by_size 进行基于大小的智能清理
    
    Args:
        base_folder: 基础文件夹路径
        days: 删除多少天之前的文件，默认7天
        file_pattern: 文件匹配模式，默认"*.png"
        
    Returns:
        tuple: (删除文件数量, 释放的空间大小(字节))
    """
    try:
        cutoff_time = datetime.now() - timedelta(days=days)
        deleted_count = 0
        deleted_size = 0
        empty_dirs = []
        
        base_path = Path(base_folder)
        if not base_path.exists():
            return 0, 0
        
        # 遍历所有年份目录
        for year_dir in base_path.iterdir():
            if not year_dir.is_dir():
                continue
                
            # 遍历所有月份目录
            for month_dir in year_dir.iterdir():
                if not month_dir.is_dir():
                    continue
                    
                # 遍历所有日期目录
                for day_dir in month_dir.iterdir():
                    if not day_dir.is_dir():
                        continue
                    
                    # 检查日期目录中的所有图片文件
                    day_has_files = False
                    for file_path in day_dir.glob(file_pattern):
                        try:
                            file_stats = file_path.stat()
                            file_created_time = datetime.fromtimestamp(file_stats.st_ctime)
                            
                            if file_created_time < cutoff_time:
                                file_size = file_stats.st_size
                                file_path.unlink()
                                deleted_count += 1
                                deleted_size += file_size
                                logger.info(f"删除过期图片: {file_path}")
                            else:
                                day_has_files = True
                        except Exception as e:
                            logger.error(f"删除文件 {file_path} 时出错: {e}")
                            day_has_files = True  # 保守处理，保留目录
                    
                    # 如果日期目录为空，标记为待删除
                    if not day_has_files and not any(day_dir.iterdir()):
                        empty_dirs.append(day_dir)
                
                # 检查月份目录是否为空
                if not any(month_dir.iterdir()):
                    empty_dirs.append(month_dir)
            
            # 检查年份目录是否为空
            if not any(year_dir.iterdir()):
                empty_dirs.append(year_dir)
        
        # 删除空目录（从最深层开始）
        for empty_dir in sorted(empty_dirs, key=lambda x: len(x.parts), reverse=True):
            try:
                empty_dir.rmdir()
                logger.info(f"删除空目录: {empty_dir}")
            except Exception as e:
                logger.error(f"删除空目录 {empty_dir} 时出错: {e}")
        
        if deleted_count > 0:
            logger.info(f"按日期清理完成，删除了 {deleted_count} 个文件，释放空间 {deleted_size / (1024*1024):.2f} MB")
        
        return deleted_count, deleted_size
    except Exception as e:
        logger.error(f"按日期清理旧图片时出错: {e}")
        return 0, 0


async def save_image_async(img, filename, save_dir="cut-images", max_size_mb=100, cleanup_days=7):
    """
    异步保存图片到日期结构的目录中
    
    Args:
        img: OpenCV图片对象
        filename: 文件名（不包含路径）
        save_dir: 基础保存目录，默认"cut-images"
        max_size_mb: 文件夹最大大小(MB)，超过后触发清理，默认100MB
        cleanup_days: 清理多少天前的文件，默认7天
    """
    def save_image():
        try:
            # 生成基于日期的完整保存路径：save_dir/yyyy/MM/dd/
            date_save_path = generate_date_path(save_dir)
            date_save_path.mkdir(parents=True, exist_ok=True)
            
            # 检查整个基础目录的大小
            base_path = Path(save_dir)
            if base_path.exists():
                folder_size = get_folder_size(base_path)
                max_size_bytes = max_size_mb * 1024 * 1024
                
                # 如果超过最大大小，先清理旧文件
                if folder_size > max_size_bytes:
                    logger.info(f"{save_dir} 文件夹大小超过 {max_size_mb}MB ({folder_size / (1024*1024):.2f}MB)，开始清理旧文件")
                    cleanup_images_by_size(base_path, max_size_mb=max_size_mb)
            
            # 保存图片到日期目录中
            save_path = date_save_path / filename
            success = cv2.imwrite(str(save_path), img)
            
            if success:
                logger.info(f"图片已保存到: {save_path}")
            else:
                logger.error(f"图片保存失败: {save_path}")
                
        except Exception as e:
            logger.error(f"异步保存图片时出错: {e}")
    
    # 在线程池中执行保存操作，避免阻塞主线程
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, save_image)


def save_image_in_background(img, filename, save_dir="cut-images", max_size_mb=100, cleanup_days=7):
    """
    在后台异步保存图片的可复用函数（支持日期目录结构）
    
    Args:
        img: OpenCV图片对象
        filename: 要保存的文件名（不包含路径）
        save_dir: 基础保存目录，默认"cut-images"
        max_size_mb: 文件夹最大大小(MB)，超过后触发清理，默认100MB
        cleanup_days: 清理多少天前的文件，默认7天
    """
    def save_in_background():
        try:
            # 在新的事件循环中运行异步保存
            def run_async_save():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(save_image_async(
                        img.copy(), filename, save_dir, max_size_mb, cleanup_days
                    ))
                finally:
                    loop.close()
            
            # 在后台线程中执行保存操作
            thread = threading.Thread(target=run_async_save)
            thread.daemon = True  # 设为守护线程，避免阻塞主程序退出
            thread.start()
            
        except Exception as e:
            logger.error(f"启动异步保存图片任务时出错: {e}")
    
    # 启动后台保存任务
    save_in_background()


def generate_unique_filename(prefix="image", page_num=None, extension="png"):
    """
    生成唯一的文件名（仅文件名，不包含路径）
    
    Args:
        prefix: 文件名前缀，默认"image"  
        page_num: 页码，可选（会添加到prefix中）
        extension: 文件扩展名，默认"png"
        
    Returns:
        str: 生成的文件名，格式：{prefix}_hh_mm_ss.{extension}
    """
    now = datetime.now()
    time_str = now.strftime("%H_%M_%S")
    
    # 如果有页码，将其添加到前缀中
    if page_num is not None:
        final_prefix = f"{prefix}_page{page_num}"
    else:
        final_prefix = prefix
    
    filename = f"{final_prefix}_{time_str}.{extension}"
    return filename


def generate_date_path(base_dir="cut-images"):
    """
    生成基于当前日期的完整保存路径
    
    Args:
        base_dir: 基础目录，默认"cut-images"
        
    Returns:
        Path: 完整的保存路径，格式：base_dir/yyyy/MM/dd/
    """
    now = datetime.now()
    year = now.strftime("%Y")
    month = now.strftime("%m")  
    day = now.strftime("%d")
    
    date_path = Path(base_dir) / year / month / day
    return date_path


class ImageSaver:
    """
    图片保存器类，提供更高级的图片保存功能
    """
    
    def __init__(self, save_dir="cut-images", max_size_mb=100, cleanup_days=7):
        """
        初始化图片保存器
        
        Args:
            save_dir: 保存目录，默认"cut-images"
            max_size_mb: 文件夹最大大小(MB)，默认100MB
            cleanup_days: 清理多少天前的文件，默认7天
        """
        self.save_dir = save_dir
        self.max_size_mb = max_size_mb
        self.cleanup_days = cleanup_days
        
        # 确保保存目录存在
        Path(self.save_dir).mkdir(exist_ok=True)
    
    def save_background(self, img, filename=None, prefix="image", page_num=None):
        """
        在后台保存图片到日期目录结构中
        
        Args:
            img: OpenCV图片对象
            filename: 指定文件名，如果为None则自动生成
            prefix: 自动生成文件名时的前缀
            page_num: 页码，用于自动生成文件名
        
        Returns:
            tuple: (保存的文件名, 完整保存路径)
        """
        if filename is None:
            filename = generate_unique_filename(prefix=prefix, page_num=page_num)
        
        save_image_in_background(
            img, filename, self.save_dir, self.max_size_mb, self.cleanup_days
        )
        
        # 返回文件名和预期的完整路径
        expected_path = generate_date_path(self.save_dir) / filename
        return filename, str(expected_path)
    
    async def save_async(self, img, filename=None, prefix="image", page_num=None):
        """
        异步保存图片到日期目录结构中
        
        Args:
            img: OpenCV图片对象
            filename: 指定文件名，如果为None则自动生成
            prefix: 自动生成文件名时的前缀
            page_num: 页码，用于自动生成文件名
        
        Returns:
            tuple: (保存的文件名, 完整保存路径)
        """
        if filename is None:
            filename = generate_unique_filename(prefix=prefix, page_num=page_num)
        
        await save_image_async(
            img, filename, self.save_dir, self.max_size_mb, self.cleanup_days
        )
        
        # 返回文件名和完整路径
        expected_path = generate_date_path(self.save_dir) / filename
        return filename, str(expected_path)
    
    def cleanup_old_files(self, days=None):
        """
        手动清理旧文件（按日期目录结构）
        注意：这是旧的按时间清理方法，建议使用 cleanup_by_size 进行基于大小的智能清理
        
        Args:
            days: 清理多少天前的文件，如果为None则使用初始化时的设置
            
        Returns:
            tuple: (删除文件数量, 释放的空间大小(字节))
        """
        if days is None:
            days = self.cleanup_days
        
        return cleanup_old_images_by_date(self.save_dir, days=days)
    
    def cleanup_by_size(self, max_size_mb=None):
        """
        基于文件夹大小的智能清理
        
        Args:
            max_size_mb: 最大大小限制(MB)，如果为None则使用初始化时的设置
            
        Returns:
            tuple: (删除文件数量, 释放的空间大小(字节))
        """
        if max_size_mb is None:
            max_size_mb = self.max_size_mb
        
        return cleanup_images_by_size(self.save_dir, max_size_mb=max_size_mb)
    
    def get_folder_info(self):
        """
        获取保存文件夹信息（按日期目录结构统计）
        
        Returns:
            dict: 包含文件夹大小、文件数量等信息
        """
        try:
            save_path = Path(self.save_dir)
            if not save_path.exists():
                return {"exists": False}
            
            total_size = get_folder_size(save_path)
            
            # 统计所有日期目录中的PNG文件
            file_count = 0
            date_dirs = []
            
            # 遍历年/月/日目录结构统计文件
            for year_dir in save_path.iterdir():
                if not year_dir.is_dir():
                    continue
                for month_dir in year_dir.iterdir():
                    if not month_dir.is_dir():
                        continue
                    for day_dir in month_dir.iterdir():
                        if not day_dir.is_dir():
                            continue
                        
                        day_files = list(day_dir.glob("*.png"))
                        if day_files:
                            date_dirs.append({
                                "date": f"{year_dir.name}-{month_dir.name}-{day_dir.name}",
                                "path": str(day_dir),
                                "file_count": len(day_files)
                            })
                            file_count += len(day_files)
            
            return {
                "exists": True,
                "path": str(save_path.absolute()),
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "file_count": file_count,
                "max_size_mb": self.max_size_mb,
                "date_directories": date_dirs,
                "structure": "yyyy/MM/dd format"
            }
        except Exception as e:
            logger.error(f"获取文件夹信息时出错: {e}")
            return {"exists": False, "error": str(e)} 