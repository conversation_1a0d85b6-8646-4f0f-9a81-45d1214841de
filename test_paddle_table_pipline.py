import time

from paddleocr import TableRecognitionPipelineV2

pipeline = TableRecognitionPipelineV2(use_doc_orientation_classify=False,
                                      use_doc_unwarping=False,
                                      use_layout_detection=True,
                                      text_detection_model_name="PP-OCRv5_mobile_det",
                                      text_recognition_model_name="PP-OCRv5_server_rec")

start = time.time()
output = pipeline.predict(input="/Users/<USER>/Coding/rh-ocr/cut-images/2025/08/28/cut_page0_10_26_26.png",
                          use_table_orientation_classify=False,
                          use_doc_unwarping=False,
                          use_doc_orientation_classify=False,
                          use_ocr_results_with_table_cells=True)
end = time.time()

print(end - start)

for res in output:
    print(res.html["table_1"])