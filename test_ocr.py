import cv2
import time
from inference.ocr_engine import OCREngine

if __name__ == "__main__":
    model = OCREngine(
        use_gpu=False,
        inference_engine='openvino')

    img = cv2.imread('/Users/<USER>/Downloads/cut_page0_17_48_09.png')
    s = time.time()
    result = model.ocr(img=img, det=True, rec_model_id="ppocrv4_doc_server")

    e = time.time()
    print("total time: {:.3f}".format(e - s))
    # print("result:", result)

    # Extract and print only the text content without coordinates
    if result:
        print("\nExtracted text:")
        for page in result:
            for line in page:
                if len(line) > 1 and isinstance(line[1], tuple):
                    text, confidence = line[1]
                    print(f"Text: {text} (Confidence: {confidence:.4f})")
                elif len(line) > 1:
                    # Handle case where line[1] might be just text
                    print(f"Text: {line[1]}")
    else:
        print("\nNo text detected")