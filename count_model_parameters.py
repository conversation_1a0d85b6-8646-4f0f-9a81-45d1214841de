#!/usr/bin/env python3
"""
PPOCRv5 模型参数量统计脚本
统计检测、识别、分类模型的参数量
"""

import onnx
import numpy as np
from pathlib import Path
import os

def count_onnx_parameters(model_path):
    """
    统计ONNX模型的参数量
    
    Args:
        model_path: ONNX模型文件路径
        
    Returns:
        tuple: (总参数量, 详细参数信息)
    """
    try:
        # 加载ONNX模型
        model = onnx.load(model_path)
        
        # 获取模型的初始化器（包含权重参数）
        initializers = model.graph.initializer
        
        total_params = 0
        param_details = []
        
        for initializer in initializers:
            # 获取参数张量的形状
            dims = initializer.dims
            if dims:
                # 计算参数数量
                param_count = np.prod(dims)
                total_params += param_count
                
                param_details.append({
                    'name': initializer.name,
                    'shape': list(dims),
                    'params': param_count
                })
        
        # 如果没有找到初始化器，尝试从节点信息估算
        if total_params == 0:
            total_params = estimate_params_from_nodes(model)
        
        return total_params, param_details
        
    except Exception as e:
        print(f"加载模型 {model_path} 时出错: {e}")
        return 0, []

def estimate_params_from_nodes(model):
    """
    从模型节点估算参数量（当权重被外部化时）
    """
    try:
        # 简单的参数估算，基于模型文件大小
        # 这是一个粗略的估算方法
        return 0
    except:
        return 0

def estimate_params_from_filesize(file_path):
    """
    根据文件大小估算参数量
    假设每个参数占用4字节（float32）
    """
    try:
        size_bytes = os.path.getsize(file_path)
        # 去掉模型结构等开销，大约80%是参数
        estimated_params = int((size_bytes * 0.8) / 4)
        return estimated_params
    except:
        return 0

def format_number(num):
    """格式化数字显示"""
    if num >= 1e9:
        return f"{num/1e9:.2f}B"
    elif num >= 1e6:
        return f"{num/1e6:.2f}M"
    elif num >= 1e3:
        return f"{num/1e3:.2f}K"
    else:
        return str(num)

def get_file_size(file_path):
    """获取文件大小（MB）"""
    size_bytes = os.path.getsize(file_path)
    return size_bytes / (1024 * 1024)

def analyze_model_structure(model_path):
    """
    分析模型结构
    """
    try:
        model = onnx.load(model_path)
        
        info = {
            'inputs': len(model.graph.input),
            'outputs': len(model.graph.output),
            'nodes': len(model.graph.node),
            'initializers': len(model.graph.initializer),
            'value_info': len(model.graph.value_info)
        }
        
        return info
    except:
        return {}

def main():
    """主函数"""
    # 定义模型路径
    model_base_path = Path("inference/models/ppocrv5")
    
    models = {
        "检测模型 (det.onnx)": model_base_path / "det" / "det.onnx",
        "检测模型 (det_server.onnx)": model_base_path / "det" / "det_server.onnx",
        "识别模型 (rec.onnx)": model_base_path / "rec" / "rec.onnx", 
        "识别模型 (rec_server.onnx)": model_base_path / "rec" / "rec_server.onnx",
        "分类模型 (cls.onnx)": model_base_path / "cls" / "cls.onnx"
    }
    
    print("=" * 80)
    print("PPOCRv5 模型参数量统计")
    print("=" * 80)
    
    total_all_params = 0
    total_estimated_params = 0
    
    for model_name, model_path in models.items():
        if model_path.exists():
            print(f"\n{model_name}")
            print("-" * 50)
            
            # 统计参数量
            total_params, param_details = count_onnx_parameters(model_path)
            
            # 获取文件大小
            file_size = get_file_size(model_path)
            
            # 根据文件大小估算参数量
            estimated_params = estimate_params_from_filesize(model_path)
            
            # 分析模型结构
            structure_info = analyze_model_structure(model_path)
            
            print(f"文件大小: {file_size:.2f} MB")
            print(f"实际参数量: {format_number(total_params)} ({total_params:,})")
            print(f"估算参数量: {format_number(estimated_params)} ({estimated_params:,})")
            
            if structure_info:
                print(f"模型结构: {structure_info['nodes']} 节点, {structure_info['initializers']} 初始化器")
            
            # 显示前几个层的参数信息
            if param_details:
                print(f"参数层数: {len(param_details)}")
                print("\n主要层参数信息:")
                # 按参数量排序，显示前5个最大的层
                sorted_details = sorted(param_details, key=lambda x: x['params'], reverse=True)[:5]
                for detail in sorted_details:
                    print(f"  - {detail['name']}: {detail['shape']} -> {format_number(detail['params'])}")
            
            # 使用实际参数量，如果为0则使用估算值
            use_params = total_params if total_params > 0 else estimated_params
            total_all_params += total_params
            total_estimated_params += estimated_params
            
        else:
            print(f"\n{model_name}: 文件不存在 ({model_path})")
    
    print("\n" + "=" * 80)
    print(f"PPOCRv5 系统实际参数量: {format_number(total_all_params)} ({total_all_params:,})")
    print(f"PPOCRv5 系统估算参数量: {format_number(total_estimated_params)} ({total_estimated_params:,})")
    print("=" * 80)
    
    # 显示不同配置的参数量对比
    print("\n模型配置对比:")
    print("-" * 30)
    
    # Mobile版本 (det.onnx + rec.onnx + cls.onnx)
    mobile_models = [
        ("det.onnx", model_base_path / "det" / "det.onnx"),
        ("rec.onnx", model_base_path / "rec" / "rec.onnx"), 
        ("cls.onnx", model_base_path / "cls" / "cls.onnx")
    ]
    
    mobile_params = 0
    mobile_estimated = 0
    
    print("\nMobile版本:")
    for name, model_path in mobile_models:
        if model_path.exists():
            params, _ = count_onnx_parameters(model_path)
            estimated = estimate_params_from_filesize(model_path)
            file_size = get_file_size(model_path)
            
            print(f"  {name}: {file_size:.2f}MB, 估算参数: {format_number(estimated)}")
            mobile_params += params
            mobile_estimated += estimated
    
    # Server版本 (det_server.onnx + rec_server.onnx + cls.onnx)
    server_models = [
        ("det_server.onnx", model_base_path / "det" / "det_server.onnx"),
        ("rec_server.onnx", model_base_path / "rec" / "rec_server.onnx"),
        ("cls.onnx", model_base_path / "cls" / "cls.onnx")
    ]
    
    server_params = 0
    server_estimated = 0
    
    print("\nServer版本:")
    for name, model_path in server_models:
        if model_path.exists():
            params, _ = count_onnx_parameters(model_path)
            estimated = estimate_params_from_filesize(model_path)
            file_size = get_file_size(model_path)
            
            print(f"  {name}: {file_size:.2f}MB, 估算参数: {format_number(estimated)}")
            server_params += params
            server_estimated += estimated
    
    print(f"\nMobile版本总估算参数量: {format_number(mobile_estimated)} ({mobile_estimated:,})")
    print(f"Server版本总估算参数量: {format_number(server_estimated)} ({server_estimated:,})")
    
    if mobile_estimated > 0 and server_estimated > 0:
        ratio = server_estimated / mobile_estimated
        print(f"Server版本是Mobile版本的 {ratio:.1f} 倍")

if __name__ == "__main__":
    main() 