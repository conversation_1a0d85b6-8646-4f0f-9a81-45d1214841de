#!/usr/bin/env python3
"""
独立的 PaddleOCR 表格识别服务
避免与 OpenVINO 的符号冲突
"""
import os
import base64
import tempfile
from pathlib import Path
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import uvicorn
import numpy as np
import cv2

# 设置环境变量避免冲突
os.environ["OMP_NUM_THREADS"] = "1"

from paddleocr import TableRecognitionPipelineV2

app = FastAPI(title="PaddleOCR Table Service")

# 初始化表格识别管道
pipeline = TableRecognitionPipelineV2(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
    use_layout_detection=True,
    text_detection_model_name="PP-OCRv5_mobile_det",
    text_recognition_model_name="PP-OCRv5_server_rec"
)


class TableRequest(BaseModel):
    image_base64: str
    use_table_orientation_classify: bool = False


class TableResponse(BaseModel):
    html_content: str
    success: bool
    error_msg: str = ""


@app.post("/predict_table", response_model=TableResponse)
async def predict_table(request: TableRequest):
    try:
        # 解码base64图片
        image_data = base64.b64decode(request.image_base64)

        # 将图片数据转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # 执行表格识别（直接使用numpy数组）
        output = pipeline.predict(
            input=img,
            use_table_orientation_classify=request.use_table_orientation_classify
        )

        html_content = ""
        for res in output:
            html_content = res.html["table_1"]

        return TableResponse(
            html_content=html_content,
            success=True
        )

    except Exception as e:
        return TableResponse(
            html_content="",
            success=False,
            error_msg=str(e)
        )


@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "paddle_table"}


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8001)
