#!/bin/bash

# PaddleOCR 表格服务启动脚本
# 用于在后台运行 paddle_table_service.py

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_FILE="$SCRIPT_DIR/paddle_table_service.py"
PID_FILE="$SCRIPT_DIR/paddle_table.pid"
LOG_FILE="$SCRIPT_DIR/logs/paddle_table.log"

# 确保日志目录存在
mkdir -p "$SCRIPT_DIR/logs"

# 检测 Python 环境
if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
    # 如果在 conda 环境中，使用 conda 环境的 python
    PYTHON_CMD="$CONDA_PREFIX/bin/python"
    echo "检测到 conda 环境: $CONDA_DEFAULT_ENV"
    echo "使用 Python: $PYTHON_CMD"
elif command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

# 验证 Python 环境是否有必要的依赖
echo "验证 Python 环境..."
if ! $PYTHON_CMD -c "import fastapi, paddleocr" 2>/dev/null; then
    echo "❌ 错误: 当前 Python 环境缺少必要的依赖 (fastapi, paddleocr)"
    echo "请确保在正确的环境中运行此脚本"
    exit 1
fi

# 检查服务是否已经在运行
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "PaddleOCR 表格服务已经在运行中 (PID: $PID)"
        exit 1
    else
        echo "清理过期的 PID 文件"
        rm -f "$PID_FILE"
    fi
fi

echo "正在启动 PaddleOCR 表格服务..."

# 启动服务并记录PID
nohup $PYTHON_CMD "$SERVICE_FILE" > "$LOG_FILE" 2>&1 &
PID=$!

# 保存PID到文件
echo $PID > "$PID_FILE"

# 等待服务启动
sleep 3

# 检查服务是否成功启动
if ps -p $PID > /dev/null 2>&1; then
    echo "PaddleOCR 表格服务启动成功!"
    echo "PID: $PID"
    echo "日志文件: $LOG_FILE"
    echo "健康检查: http://127.0.0.1:8001/health"
    
    # 测试健康检查接口
    echo "正在测试服务连接..."
    sleep 2
    if curl -s http://127.0.0.1:8001/health > /dev/null; then
        echo "✅ 服务运行正常，可以接收请求"
    else
        echo "⚠️  服务可能还在初始化中，请稍后再试"
    fi
else
    echo "❌ 服务启动失败，请检查日志文件: $LOG_FILE"
    rm -f "$PID_FILE"
    exit 1
fi
